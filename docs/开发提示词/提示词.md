# 分章节评审的一致性问题
我要实现报告评审助手，根据审查细则用大模型服务对可信性研究报告进行评审，评审标准相关文档有：可研报告的审查指南、可研报告的编制大纲。 由于大模型上下文有限，而报告内容很长，因此采用分章节方式进行评审,即每次把1个章节的内容和所有评审相关文档发给大模型服务进行评审。但是章节之间的内容或数据一致性如何检查？比如第1章提到损耗率=0.2， 第5章写的是0.3，分章节调用大模型如何检查这种一致性？

# 报告专题管理功能
增加一个报告专题管理功能，要求:
1.每个报告专题包括：审查指南、审查细则、可研编制大纲。
2.上传报告时，可以指定一个报告专题。
3.对可研报告评审时，根据所属的报告专题，系统自动加载该专题的审查指南、审查细则、可研编制大纲。
4.前端UI优化：增加菜单管理，包括报告专题管理菜单、可研报告管理
  4.1 报告专题管理菜单：可以添加、修改、删除报告专题
  4.2 报告管理菜单：可以查看、修改、删除报告，可以指定所属的报告专题，查询过滤报告专题下的报告。
  4.3 报告评审菜单：可以选择报告进行评审，可研查看、修改、删除报告评审记录。
5.报告评审结果暂时以json格式保存, 报告可选择重新评审。
6.专题管理的元数据也暂时用文件系统json格式存储到当前data/目录，后续考虑用数据库存储。